<template>
    <view id="app">
        <router-view></router-view>
    </view>
</template>
<script setup lang="ts">
// import routingIntercept from '@/permission'
import shareManager from '@/utils/shareManager'

onLaunch((options) => {
    // routingIntercept()
    uni.hideTabBar()

    // 处理分享参数
    console.log('App Launch Options:', options)

    // 检查是否有分享参数
    if (options.query && Object.keys(options.query).length > 0) {
        shareManager.setShareParams(options.query)
    }
})

onShow((options) => {
    console.log('App Show Options:', options)

    // 处理从分享链接进入的情况
    if (options && options.query && Object.keys(options.query).length > 0) {
        shareManager.setShareParams(options.query)
    }
})

onHide(() => {
    console.log('App Hide')
})
// 全局变量
// provide('globalObj', <globalObjInt>{
// 	// 公用跳转方法
// 	goToPage
// });
// // 引入静态资源
</script>

<style lang="scss">
/* 注意要写在第一行，同时给style标签加入lang="scss"属性 */
@import 'uview-plus/index.scss';
</style>
