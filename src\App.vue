<template>
    <view id="app">
        <router-view></router-view>
    </view>
</template>
<script setup lang="ts">
// import routingIntercept from '@/permission'

onLaunch((options) => {
    // routingIntercept()
    uni.hideTabBar()

    // 处理分享参数
    console.log('🔥 App Launch Options:', options)
    console.log('🔥 App Launch - options.query:', options.query)
    console.log('🔥 App Launch - options.scene:', options.scene)
    console.log('🔥 App Launch - options.path:', options.path)

    // 检查是否有分享参数
    if (options.query && Object.keys(options.query).length > 0) {
        console.log('🔥 App Launch - 检测到分享参数，保存到缓存')
        // 直接保存到缓存，不使用 shareManager
        uni.setStorageSync('app_share_params', options.query)
        uni.setStorageSync('share_params_processed', false)
    } else {
        console.log('🔥 App Launch - 没有分享参数')
    }
})

onShow((options) => {
    console.log('🔥 App Show Options:', options)
    console.log('🔥 App Show - options.query:', options?.query)
    console.log('🔥 App Show - options.scene:', options?.scene)
    console.log('🔥 App Show - options.path:', options?.path)

    // 处理从分享链接进入的情况
    if (options && options.query && Object.keys(options.query).length > 0) {
        console.log('🔥 App Show - 检测到分享参数，保存到缓存')
        // 直接保存到缓存，不使用 shareManager
        uni.setStorageSync('app_share_params', options.query)
        uni.setStorageSync('share_params_processed', false)
    } else {
        console.log('🔥 App Show - 没有分享参数')
    }
})

onHide(() => {
    console.log('App Hide')
})
// 全局变量
// provide('globalObj', <globalObjInt>{
// 	// 公用跳转方法
// 	goToPage
// });
// // 引入静态资源
</script>

<style lang="scss">
/* 注意要写在第一行，同时给style标签加入lang="scss"属性 */
@import 'uview-plus/index.scss';
</style>
