<script lang="ts" setup>
const props = withDefaults(
    defineProps<{
        current: string
    }>(),
    {
        current: 'home'
    }
)

const tabber_agent = [
    {
        text: '首页',
        name: 'home',
        url: '/pages/index/index'
    },
    {
        text: '课程商店',
        name: 'course',
        url: '/pages/course/index'
    },
    {
        text: '活动报名',
        name: 'activity',
        url: '/pages/activity/index'
    },
    {
        text: '周边商店',
        name: 'surrounding',
        url: '/pages/surrounding/index'
    },
    {
        text: '我的',
        name: 'mine',
        url: '/pages/mine/index'
    }
]

const jumpPages = ({ name, url }: { name: string; url: string }) => {
    if (name === props.current) return
    uni.switchTab({ url })
}
</script>
<template>
    <view class="footer-box">
        <view class="ex-tabbar" id="ex-tabbar">
            <view class="ex-tabbar-body">
                <!-- <image class="back" src="@/static/tabbar.png" /> -->
                <block v-for="(v, k) of tabber_agent" :key="k">
                    <view
                        class="flex column ex-tabbar__items agent"
                        :class="{ 'ex-tabbar__active': current === v.name }"
                        @tap.stop="jumpPages({ name: v.name, url: v.url })"
                    >
                        <image v-if="current === v.name" class="icon" :src="`/static/${v.name}_act.png`" />
                        <image v-else class="icon" :src="`/static/${v.name}.png`" />
                        <text>{{ v.text }}</text>
                    </view>
                </block>
            </view>
        </view>
    </view>
</template>

<style scoped lang="scss">
.footer-box {
    .ex-tabbar {
        width: 100%;
        position: fixed;
        z-index: 991;
        left: 0;
        bottom: 0;
        background-color: #1a2b4a;
        filter: drop-shadow(0px 0 10rpx #0000001a);

        &-body {
            box-sizing: border-box;
            padding: 18rpx 18rpx 30rpx;
            position: relative;
            height: 100%;
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            align-items: center;
            gap: 20rpx;

            .back {
                position: absolute;
                top: 0;
                left: 0;
                opacity: 0.8;
                z-index: -1;
            }
        }

        &__items {
            height: fit-content;

            .icon {
                width: 50rpx;
                height: 50rpx;
                margin-bottom: 10rpx;
            }

            text {
                font-size: 24rpx;
                color: #fff;
            }
        }

        &__active {
            transition: all 0.3s ease;
            padding: 8rpx 0;
            border-radius: 30rpx;
            background-color: #fff;

            .icon {
                width: 50rpx;
                height: 50rpx;
                margin-bottom: 10rpx;
            }

            text {
                font-size: 24rpx;
                color: #25a3dd;
            }
        }
    }
}
</style>
