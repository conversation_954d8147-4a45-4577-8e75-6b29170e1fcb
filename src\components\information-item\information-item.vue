<script setup lang="ts">
import { getImageUrl, getPlainText } from '@/utils/common'
const props = withDefaults(
    defineProps<{
        item: { [n: string]: any }
        isShowTools?: boolean
    }>(),
    {
        item: () => ({}),
        isShowTools: false
    }
)
const emits = defineEmits(['cancelCollect'])
const navto = (url: string, params = {}) => uni.$util.goToPage({ url, params })

// 阻止事件冒泡的取消收藏函数
const cancelCollect = () => {
    emits('cancelCollect', { id: props.item.id })
}

// 阻止分享按钮事件冒泡
// const handleShareClick = (event: Event) => {
//     event.stopPropagation() // 阻止事件冒泡
// }

watch(
    props.item,
    () => {
        props.item.content = getPlainText(props.item.content) as string
    },
    {
        deep: true,
        immediate: true
    }
)
</script>
<template>
    <view class="information-item" @tap.stop="navto('subPages/Information/details', { id: props.item.id })">
        <!-- <view class="flex-start-start title_box">
            <image class="avatar" :src="getImageUrl('upload/file/logo.jpg')" />
            <view class="flex1 right_box">
                <view class="flex flex1 title">
                    <view class="text-ellipsis flex1">LNT(中国)官方</view>
                    <image src="@/static/v.png" />
                </view>
                <view class="timer">发布于：{{ props.item.create_time_text || props.item.create_time }}</view>
            </view>
        </view> -->
        <view class="tit text-ellipsis">{{ props.item.title }}</view>
        <view class="timer">发布时间：{{ props.item.create_time_text || props.item.create_time }}</view>
        <view class="content text-ellipsis-3">{{ props.item.content }}</view>
        <image class="photo" :src="getImageUrl(props.item.pic_path)" mode="aspectFill" />
        <view class="flex-center-end" v-if="props.isShowTools">
            <image src="@/static/star_act.png" mode="widthFix" @tap.stop="cancelCollect" />
            <!-- <button class="flex share-btn" open-type="share" @tap.stop="handleShareClick">
                <image src="@/static/shares.png" mode="widthFix" />
            </button> -->
        </view>
    </view>
</template>

<style scoped lang="scss">
.information-item {
    margin-top: 30rpx;
    padding: 30rpx;
    border-radius: 18rpx;
    background-color: #2f3e5a;
    color: #fff;

    .title_box {
        .avatar {
            width: 110rpx;
            height: 110rpx;
            margin-right: 30rpx;
            border-radius: 90rpx;
        }

        .right_box {
            .title {
                .text-ellipsis {
                    width: 0;
                    font-size: 28rpx;
                    color: #ffffff;
                }

                image {
                    margin-left: 10rpx;
                    width: 40rpx;
                    height: 40rpx;
                }
            }
        }
    }

    .tit {
        text-align: center;
        font-weight: 700;
        font-size: 32rpx;
    }

    .timer {
        margin: 30rpx 0 18rpx;
        font-size: 28rpx;
        color: #ffffff;
        text-align: center;
    }

    .photo {
        width: 100%;
        margin-top: 20rpx;
        height: 360rpx;
        border-radius: 18rpx;
    }

    .content {
        opacity: 0.8;
        font-size: 28rpx;
        line-height: 56rpx;
    }

    .flex-center-end {
        image {
            width: 60rpx;

            &:last-child {
                width: 55rpx;
                margin-left: 30rpx;
            }
        }

        button {
            width: fit-content;
            margin: 0;
            padding: 0;
            background: transparent;
            border: none;

            &.share-btn {
                display: flex;
                align-items: center;
                justify-content: center;
            }

            &::after {
                border: none;
            }
        }
    }
}
</style>
