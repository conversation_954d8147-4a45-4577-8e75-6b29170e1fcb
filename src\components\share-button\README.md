# ShareButton 分享组件

## 功能特性

✅ **自动携带推荐人参数** - 分享时自动携带当前用户的 `member_id` 作为推荐人ID  
✅ **支持朋友圈分享** - 同时支持微信好友和朋友圈分享  
✅ **防止事件冒泡** - 内置 `@tap.stop` 防止与外层点击事件冲突  
✅ **高度可定制** - 支持自定义图标、文字、样式等  
✅ **TypeScript 支持** - 完整的类型定义  

## 基础用法

```vue
<template>
    <!-- 最简单的用法 -->
    <ShareButton />
    
    <!-- 自定义分享内容 -->
    <ShareButton 
        title="精彩课程推荐"
        :path="`/subPages/class/classDetail?id=${courseId}`"
        :imageUrl="courseImage"
    />
    
    <!-- 自定义样式 -->
    <ShareButton 
        :showText="true"
        text="分享给好友"
        :buttonStyle="{ padding: '10rpx 20rpx', backgroundColor: '#007aff' }"
        :textStyle="{ color: '#fff', fontSize: '28rpx' }"
    />
</template>

<script setup>
import ShareButton from '@/components/share-button/share-button.vue'
</script>
```

## Props 参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| title | string | '精彩内容分享' | 分享标题 |
| imageUrl | string | '' | 分享图片URL |
| path | string | '/pages/index/index' | 分享页面路径（不含参数） |
| showIcon | boolean | true | 是否显示图标 |
| iconSrc | string | '/static/share.png' | 图标地址 |
| showText | boolean | false | 是否显示文字 |
| text | string | '分享' | 按钮文字 |
| buttonStyle | object | {} | 按钮自定义样式 |
| iconStyle | object | {width:'40rpx',height:'40rpx'} | 图标自定义样式 |
| textStyle | object | {} | 文字自定义样式 |

## 分享参数自动处理

### 分享时自动添加推荐人参数

当用户点击分享时，组件会自动：
1. 获取当前用户的 `userStore.userInfo.member_id`
2. 将其作为 `pid` 参数添加到分享链接中
3. 生成类似 `/pages/index/index?pid=12345` 的分享链接

### 接收分享参数的处理流程

1. **App.vue** - 在 `onLaunch` 和 `onShow` 中捕获分享参数
2. **ShareManager** - 全局管理分享参数状态
3. **首页 onShow** - 处理分享参数，保存推荐人ID到缓存
4. **登录页面** - 登录时自动携带缓存的推荐人ID

## 在不同页面中的使用示例

### 资讯详情页
```vue
<ShareButton 
    :title="article.title"
    :imageUrl="article.cover"
    :path="`/subPages/Information/details?id=${article.id}`"
/>
```

### 课程详情页
```vue
<ShareButton 
    :title="course.name"
    :imageUrl="course.cover"
    :path="`/subPages/class/classDetail?id=${course.id}`"
/>
```

### 活动详情页
```vue
<ShareButton 
    :title="activity.title"
    :imageUrl="activity.banner"
    :path="`/subPages/active/activeDetail?id=${activity.id}`"
/>
```

## 注意事项

1. **推荐人参数** - 只有登录用户才会携带推荐人参数
2. **图片路径** - 分享图片建议使用完整的网络地址
3. **路径格式** - path 参数不要包含查询参数，组件会自动添加 pid
4. **事件冒泡** - 组件已内置防止事件冒泡，无需额外处理

## 技术实现

- 基于 `onShareAppMessage` 和 `onShareTimeline` 实现
- 使用 Pinia store 获取用户信息
- 自动处理 URL 参数拼接
- 支持 slot 自定义内容
