<template>
    <button 
        class="share-button" 
        open-type="share" 
        @tap.stop="handleShareClick"
        :style="buttonStyle"
    >
        <image 
            v-if="showIcon" 
            :src="iconSrc" 
            :style="iconStyle"
            mode="widthFix" 
        />
        <text v-if="showText" :style="textStyle">{{ text }}</text>
        <slot></slot>
    </button>
</template>

<script setup lang="ts">
import useUserStore from '@/store/user'

interface Props {
    // 分享标题
    title?: string
    // 分享图片
    imageUrl?: string
    // 分享路径（不包含参数）
    path?: string
    // 是否显示图标
    showIcon?: boolean
    // 图标地址
    iconSrc?: string
    // 是否显示文字
    showText?: boolean
    // 按钮文字
    text?: string
    // 按钮样式
    buttonStyle?: Record<string, any>
    // 图标样式
    iconStyle?: Record<string, any>
    // 文字样式
    textStyle?: Record<string, any>
}

const props = withDefaults(defineProps<Props>(), {
    title: '精彩内容分享',
    imageUrl: '',
    path: '/pages/index/index',
    showIcon: true,
    iconSrc: '/static/share.png',
    showText: false,
    text: '分享',
    buttonStyle: () => ({}),
    iconStyle: () => ({ width: '40rpx', height: '40rpx' }),
    textStyle: () => ({})
})

const userStore = useUserStore()

// 处理分享按钮点击
const handleShareClick = (event: Event) => {
    event.stopPropagation() // 阻止事件冒泡
}

// 分享配置
onShareAppMessage(() => {
    // 获取当前用户的member_id作为推荐人ID
    const currentUserId = userStore.userInfo?.member_id
    
    // 构建分享路径，携带推荐人参数
    let sharePath = props.path
    if (currentUserId) {
        const separator = sharePath.includes('?') ? '&' : '?'
        sharePath = `${sharePath}${separator}pid=${currentUserId}`
    }
    
    console.log('分享配置:', {
        title: props.title,
        imageUrl: props.imageUrl,
        path: sharePath
    })
    
    return {
        title: props.title,
        imageUrl: props.imageUrl || '/static/logo.png', // 默认分享图片
        path: sharePath
    }
})

// 分享到朋友圈配置
onShareTimeline(() => {
    const currentUserId = userStore.userInfo?.member_id
    
    let sharePath = props.path
    if (currentUserId) {
        const separator = sharePath.includes('?') ? '&' : '?'
        sharePath = `${sharePath}${separator}pid=${currentUserId}`
    }
    
    console.log('朋友圈分享配置:', {
        title: props.title,
        imageUrl: props.imageUrl,
        query: currentUserId ? `pid=${currentUserId}` : ''
    })
    
    return {
        title: props.title,
        imageUrl: props.imageUrl || '/static/logo.png',
        query: currentUserId ? `pid=${currentUserId}` : ''
    }
})
</script>

<style lang="scss" scoped>
.share-button {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0;
    padding: 0;
    background: transparent;
    border: none;
    
    &::after {
        border: none;
    }
}
</style>
