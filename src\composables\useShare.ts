/**
 * 全局分享功能组合式函数
 * 支持微信小程序原生分享功能，自动携带推荐人参数
 */
import { onShareAppMessage, onShareTimeline } from '@dcloudio/uni-app'
import useUserStore from '@/store/user'

export interface ShareConfig {
    title?: string
    imageUrl?: string
    path?: string
    query?: string
}

export interface UseShareOptions {
    // 自定义分享标题
    title?: string
    // 自定义分享图片
    imageUrl?: string
    // 自定义分享路径（不包含推荐人参数）
    path?: string
    // 是否跳转到首页（默认true，除了资讯详情页）
    redirectToHome?: boolean
    // 额外的查询参数
    extraQuery?: Record<string, string | number>
    // 动态获取分享标题的函数
    getTitleFn?: () => string
    // 动态获取分享图片的函数
    getImageUrlFn?: () => string
    // 动态获取额外查询参数的函数
    getExtraQueryFn?: () => Record<string, string | number>
}

/**
 * 使用分享功能
 * @param options 分享配置选项
 */
export function useShare(options: UseShareOptions = {}) {
    const userStore = useUserStore()

    // 默认配置
    const defaultConfig = {
        title: 'LNT(中国) - 专业培训平台，一起来学习吧！',
        imageUrl: '/static/logo.png',
        path: '/pages/index/index',
        redirectToHome: true
    }

    // 合并配置
    const config = { ...defaultConfig, ...options }

    /**
     * 构建分享路径
     */
    const buildSharePath = (basePath: string, extraQuery?: Record<string, string | number>) => {
        const currentUserId = userStore.userInfo?.member_id
        console.log('🔥 构建分享路径 - 当前用户ID:', currentUserId)
        console.log('🔥 构建分享路径 - 基础路径:', basePath)

        // 如果设置了跳转到首页，则使用首页路径
        let finalPath = config.redirectToHome ? '/pages/index/index' : basePath

        // 构建查询参数
        const queryParams: Record<string, string | number> = {}

        // 添加额外查询参数
        if (extraQuery) {
            Object.assign(queryParams, extraQuery)
        }

        // 添加推荐人参数
        if (currentUserId) {
            queryParams.pid = currentUserId
        }

        // 构建查询字符串
        const queryString = Object.keys(queryParams)
            .map(key => `${key}=${queryParams[key]}`)
            .join('&')

        if (queryString) {
            finalPath += `?${queryString}`
        }

        console.log('🔥 构建分享路径 - 最终路径:', finalPath)
        return finalPath
    }

    /**
     * 设置分享给朋友功能
     */
    const setupShareAppMessage = () => {
        onShareAppMessage(() => {
            console.log('🔥 分享给朋友 - 开始')

            // 动态获取分享配置
            const dynamicTitle = options.getTitleFn ? options.getTitleFn() : config.title
            const dynamicImageUrl = options.getImageUrlFn ? options.getImageUrlFn() : config.imageUrl
            const dynamicExtraQuery = options.getExtraQueryFn ? options.getExtraQueryFn() : config.extraQuery

            const sharePath = buildSharePath(config.path!, dynamicExtraQuery)

            const shareConfig: ShareConfig = {
                title: dynamicTitle,
                imageUrl: dynamicImageUrl,
                path: sharePath
            }

            console.log('🔥 分享给朋友 - 配置:', shareConfig)
            return shareConfig
        })
    }

    /**
     * 设置分享到朋友圈功能
     */
    const setupShareTimeline = () => {
        onShareTimeline(() => {
            console.log('🔥 分享到朋友圈 - 开始')

            const currentUserId = userStore.userInfo?.member_id

            // 动态获取分享配置
            const dynamicTitle = options.getTitleFn ? options.getTitleFn() : config.title
            const dynamicImageUrl = options.getImageUrlFn ? options.getImageUrlFn() : config.imageUrl
            const dynamicExtraQuery = options.getExtraQueryFn ? options.getExtraQueryFn() : config.extraQuery

            // 构建查询参数
            const queryParams: Record<string, string | number> = {}

            // 添加额外查询参数
            if (dynamicExtraQuery) {
                Object.assign(queryParams, dynamicExtraQuery)
            }

            // 添加推荐人参数
            if (currentUserId) {
                queryParams.pid = currentUserId
            }

            // 构建查询字符串
            const queryString = Object.keys(queryParams)
                .map(key => `${key}=${queryParams[key]}`)
                .join('&')

            const shareConfig: ShareConfig = {
                title: dynamicTitle,
                imageUrl: dynamicImageUrl,
                query: queryString
            }

            console.log('🔥 分享到朋友圈 - 配置:', shareConfig)
            return shareConfig
        })
    }

    /**
     * 处理分享参数（推荐人逻辑）
     */
    const handleShareParams = async () => {
        try {
            console.log('🔥 处理分享参数 - 开始')

            // 从缓存中获取推荐人ID
            const referrerId = uni.getStorageSync('referrer_pid')
            console.log('🔥 处理分享参数 - referrerId:', referrerId)

            // 检查是否有推荐人ID且用户已登录
            if (referrerId && userStore.checkLogin) {
                console.log('🔥 用户已登录，立即更新推荐关系')
                await updateReferrerRelation(referrerId)
            } else if (referrerId && !userStore.checkLogin) {
                console.log('🔥 用户未登录，显示提示信息')
                // 用户未登录，显示提示信息
                uni.showToast({
                    title: '检测到推荐链接，请先登录',
                    icon: 'none',
                    duration: 2000
                })
            } else {
                console.log('🔥 没有推荐人ID需要处理')
            }
        } catch (error) {
            console.error('🔥 处理分享参数失败:', error)
        }
    }

    /**
     * 更新推荐关系
     */
    const updateReferrerRelation = async (referrerId: string) => {
        try {
            // 这里可以调用API更新推荐关系
            // 例如：await updateReferrer({ pid: referrerId })
            console.log('🔥 更新推荐关系:', referrerId)

            // 注意：这里不清除缓存，让登录页面来处理
            // 因为用户可能还没有完成登录流程

            uni.showToast({
                title: '推荐关系已记录，请完成登录',
                icon: 'none',
                duration: 2000
            })
        } catch (error) {
            console.error('🔥 更新推荐关系失败:', error)
            uni.showToast({
                title: '推荐关系记录失败',
                icon: 'none',
                duration: 2000
            })
        }
    }

    // 自动设置分享功能
    setupShareAppMessage()
    setupShareTimeline()

    return {
        handleShareParams,
        updateReferrerRelation,
        buildSharePath
    }
}
