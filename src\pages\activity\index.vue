<script setup lang="ts">
import exTabbar from '@/components/ex-tabbar/ex-tabbar.vue'
import { activitycategory, activityList } from '@/api/active/index'
import { getHeaderImage, getImageUrl, getPlainText } from '@/utils/common'
const orderListRef = ref()
const currentSort = ref(0)
const query = reactive({ title: '', category_id: '' })

const cardList = ref<{ id: number; ico_path: string; title: string }[]>([])

const cardClick = (e: any, ind: number) => {
    currentSort.value = ind
    query.category_id = e.id
    orderListRef.value.refreshFn()
    // uni.navigateTo({
    //     url: '/subPages/active/activeList?id=' + e.id
    // })
}

const search = uni.$util.debounce(() => {
    orderListRef.value.refreshFn()
})
const doSearch = (formData: { page: number; limit: number; title: string; category_id: number }, onSuccess: Function) => {
    const submitData = { ...formData }
    activityList(submitData).then((res) => {
        const { data } = res as { data: { data: any; total: number } }
        data.data.forEach((item: any) => {
            item.introduce = getPlainText(item.introduce)
            item.introduce = item.introduce.replace(/&nbsp;/g, '')
        })
        onSuccess({ data })
    })
}

const godetail = (id: string) => {
    uni.navigateTo({
        url: '/subPages/active/activeDetail?id=' + id
    })
}
// const xzword = (str: string) => {
//     return str.slice(0, 4)
// }

onShow(async () => {
    await activitycategory().then((res: any) => {
        res.data.unshift({ id: '', title: '全部' })
        cardList.value = res.data
    })
})
</script>
<template>
    <view class="activity">
        <ex-header title="活动" background-color="#1a2b4a" text-color="#fff">
            <template #left>
                <image style="width: 160rpx" src="@/static/img/icon1.png" mode="aspectFill"></image>
            </template>
        </ex-header>
        <view class="index">
            <u-search
                v-model="query.title"
                :placeholder="'请输入搜索关键词'"
                bg-color="#f7f8fa"
                :action-style="{ color: '#FFFFFF' }"
                @custom="search"
                @search="search"
            ></u-search>
            <view class="title">分类</view>
            <view class="cardlist">
                <view
                    class="cardone"
                    :class="{ card_act: index == currentSort }"
                    v-for="(item, index) in cardList"
                    :key="index"
                    @click="cardClick(item, index)"
                >
                    <image :src="item.title === '全部' ? '/static/all.png' : getHeaderImage(item.ico_path)" />
                    <view class="text-ellipsis" style="max-width: 40px">{{ item.title }}</view>
                </view>
            </view>
            <view class="title2">最新活动</view>
            <view class="kcbox">
                <ex-list ref="orderListRef" :options="query" :on-form-search="doSearch">
                    <template v-slot="{ row }">
                        <view class="kcone" @click="godetail(row.id)">
                            <view
                                class="bm"
                                :style="
                                    row.statusArr.name == '报名中'
                                        ? 'background-color: #25A3DD'
                                        : row.statusArr.name == '待开始'
                                          ? 'background-color: #056C81'
                                          : row.statusArr.name == '已满员'
                                            ? 'background-color: #F59B21'
                                            : row.statusArr.name == '活动中'
                                              ? 'background-color: #8CC046'
                                              : 'background-color: #AAAAAA'
                                "
                            >
                                {{ row.statusArr.name }}
                            </view>
                            <image style="height: 386rpx" :src="getImageUrl(row.pic_path)" mode="aspectFill"></image>
                            <view class="title1">{{ row.title }}</view>
                            <view class="time">活动时间：{{ row.activity_start_time }} ~ {{ row.activity_end_time }}</view>
                            <view class="ms">{{ row.introduce || '暂无介绍' }}</view>
                            <view class="bottompart">
                                <view class="class">
                                    {{ row.category_id_name }}
                                </view>
                                <view class="more">查看更多 >></view>
                            </view>
                        </view>
                    </template>
                </ex-list>
            </view>
        </view>
        <ex-tabbar current="activity"></ex-tabbar>
    </view>
</template>

<style scoped lang="scss">
.activity {
    padding: 30rpx 30rpx 160rpx;
    box-sizing: border-box;
}

.index {
    .title {
        margin: 36rpx 0;
        font-family: Source Han Sans;
        font-size: 32rpx;
        font-weight: normal;
        line-height: normal;
        letter-spacing: normal;
        color: #ffffff;
    }

    .title2 {
        margin-top: 40rpx;
        font-family: Source Han Sans;
        font-size: 32rpx;
        font-weight: normal;
        line-height: normal;
        letter-spacing: normal;
        color: #ffffff;
    }

    .cardlist {
        display: flex !important;
        flex-wrap: wrap !important;
        gap: 18rpx !important;
        justify-content: flex-start !important;
        align-items: flex-start !important;
        width: 100% !important;
        box-sizing: border-box !important;

        .cardone {
            // 精确计算宽度：考虑容器宽度和间距
            width: calc((100% - 90rpx) / 5) !important;
            max-width: calc((100% - 90rpx) / 5) !important;
            min-width: 0 !important;
            padding: 12rpx 8rpx !important;
            flex-shrink: 0 !important;
            display: flex !important;
            flex-direction: column !important;
            align-items: center !important;
            justify-content: flex-start !important;
            box-sizing: border-box !important;

            image {
                width: 80rpx !important;
                height: 80rpx !important;
                margin-bottom: 6rpx !important;
                flex-shrink: 0 !important;
            }

            view {
                text-align: center !important;
                width: 100% !important;
                font-size: 24rpx !important;
                color: #ffffff !important;
                // 文字溢出隐藏
                overflow: hidden !important;
                white-space: nowrap !important;
                text-overflow: ellipsis !important;
                line-height: 1.2 !important;
            }

            &.card_act {
                border-radius: 20rpx !important;
                background-color: #fff !important;

                view {
                    font-weight: 700 !important;
                    color: #0e2942 !important;
                }
            }
        }
    }

    .kong_box {
        display: flex !important;
        flex-wrap: wrap !important;
        gap: 18rpx !important;
        justify-content: flex-start !important;
        align-items: flex-start !important;
        width: 100% !important;
        box-sizing: border-box !important;

        .kong_item {
            // 精确计算宽度：(100% - 5个间距 * 18rpx) / 6
            width: calc((100% - 90rpx) / 6) !important;
            max-width: calc((100% - 90rpx) / 6) !important;
            min-width: 0 !important;
            display: flex !important;
            flex-direction: column !important;
            align-items: center !important;
            justify-content: flex-start !important;
            box-sizing: border-box !important;
            padding: 12rpx 8rpx !important;

            image {
                width: 80rpx !important;
                height: 80rpx !important;
                margin-bottom: 6rpx !important;
                flex-shrink: 0 !important;
            }

            .desc {
                text-align: center !important;
                width: 100% !important;
                font-size: 24rpx !important;
                color: #ffffff !important;
                // 文字溢出隐藏 - 正确的设置方式
                overflow: hidden !important;
                white-space: nowrap !important;
                text-overflow: ellipsis !important;
                line-height: 1.2 !important;
                // 确保不会超出父容器
                max-width: 100% !important;
                box-sizing: border-box !important;
            }
        }
    }

    .kcbox {
        padding: 0 0 40rpx 0;
        box-sizing: border-box;
        overflow-y: auto;
        width: 100%;
        max-width: 100%;

        :first-child {
            margin-top: 0 !important;
        }

        .kcone {
            display: grid;
            margin-top: 60rpx;
            width: 100%;
            max-width: 100%;
            box-sizing: border-box;

            .bm {
                width: 100rpx;
                height: 40rpx;
                border-radius: 40rpx;
                font-family: Source Han Sans;
                font-size: 26rpx;
                font-weight: normal;
                line-height: normal;
                text-align: center;
                display: flex;
                align-items: center;
                justify-content: center;
                letter-spacing: normal;
                color: #ffffff;
                position: relative;
                top: 40rpx;
            }

            .title1 {
                margin-top: 32rpx;
                font-family: Source Han Sans;
                font-size: 32rpx;
                font-weight: normal;
                line-height: normal;
                letter-spacing: normal;
                color: #ffffff;
            }

            .time {
                margin-top: 8rpx;
                font-family: Source Han Sans;
                font-size: 24rpx;
                font-weight: normal;
                line-height: normal;
                letter-spacing: normal;
                color: #ffffff;
            }

            .ms {
                margin-top: 6rpx;
                font-family: Source Han Sans;
                font-size: 24rpx;
                font-weight: normal;
                line-height: normal;
                letter-spacing: normal;
                color: #ffffff;
                display: -webkit-box;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 2;
                overflow: hidden;
                text-overflow: ellipsis;
                width: 100%;
                /* 根据实际需要设置宽度 */
            }

            .bottompart {
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-top: 18rpx;

                .class {
                    padding: 5rpx 20rpx;
                    border-radius: 40rpx;
                    background-color: #ffffff;
                    font-family: Source Han Sans;
                    font-size: 26rpx;
                    font-weight: normal;
                    line-height: normal;
                    text-align: center;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    letter-spacing: normal;
                    color: #25a3dd;
                }

                .more {
                    font-family: Source Han Sans;
                    font-size: 28rpx;
                    font-weight: normal;
                    line-height: normal;
                    letter-spacing: normal;
                    color: #25a3dd;
                }
            }
        }
    }
}
</style>
