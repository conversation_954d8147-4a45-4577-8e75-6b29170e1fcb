<script setup lang="ts">
import { getCourseCategory, getCourse } from '@/api/course/index'
import { getHeaderImage, getImageUrl, getPlainText } from '@/utils/common'
import exTabbar from '@/components/ex-tabbar/ex-tabbar.vue'
import { useShare } from '@/composables/useShare'

// 使用全局分享功能 - 课程页面跳转到首页
const { handleShareParams, getShareAppMessageConfig, getShareTimelineConfig } = useShare({
    title: 'LNT(中国) - 专业课程培训，提升你的技能！',
    imageUrl: '/static/logo.png',
    path: '/pages/course/index',
    redirectToHome: true // 课程页面分享后跳转到首页
})

// 设置分享给朋友功能
onShareAppMessage(() => {
    return getShareAppMessageConfig()
})

// 设置分享到朋友圈功能
onShareTimeline(() => {
    return getShareTimelineConfig()
})

const orderListRef = ref()
const currentSort = ref(0)
const query = reactive({ title: '', category_id: '' })
const cardList = ref<{ id: number; ico_path: string; title: string }[]>([])
const getSortList = () => {
    getCourseCategory().then((res: any) => {
        res.data.unshift({ id: '', title: '全部' })
        cardList.value = res.data
    })
}

const cardClick = (e: any, ind: number) => {
    currentSort.value = ind
    query.category_id = e.id
    orderListRef.value.refreshFn()
    // uni.navigateTo({
    //     url: '/subPages/class/classList?id=' + e.id
    // })
}

const search = uni.$util.debounce(() => {
    orderListRef.value.refreshFn()
})

const doSearch = (formData: { page: number; limit: number; title: string; category_id: number }, onSuccess: Function) => {
    const submitData = { ...formData }
    getCourse(submitData).then((res) => {
        const { data } = res as { data: { data: any; total: number } }
        data.data.forEach((item: any) => {
            item.content = getPlainText(item.content)
            item.content = item.content.replace(/&nbsp;/g, '')
        })
        onSuccess({ data })
    })
}
// const xzword = (str: string) => {
//     return str.slice(0, 4)
// }

const godetail = (id: string) => {
    uni.navigateTo({
        url: '/subPages/class/classDetail?id=' + id
    })
}

onShow(async () => {
    // 处理分享参数（推荐人逻辑）
    await handleShareParams()

    getSortList()
})
</script>
<template>
    <view class="course">
        <ex-header title="课程" background-color="#1a2b4a" text-color="#fff">
            <template #left>
                <image style="width: 160rpx" src="@/static/img/icon1.png" mode="aspectFill"></image>
            </template>
        </ex-header>
        <u-search
            placeholder="请输入关键字"
            v-model="query.title"
            bg-color="#f7f8fa"
            :action-style="{ fontFamily: 'Source Han Sans', fontSize: '28rpx', color: '#FFFFFF' }"
            @custom="search"
            @search="search"
        />
        <view class="title">分类</view>
        <view class="flex-start-start wrap cardlist">
            <view
                class="flex column cardone"
                :class="{ card_act: index == currentSort }"
                v-for="(item, index) in cardList"
                :key="index"
                @click="cardClick(item, index)"
            >
                <image :src="item.title === '全部' ? '/static/all.png' : getHeaderImage(item.ico_path)" />
                <view class="text-ellipsis">{{ item.title }}</view>
            </view>
        </view>
        <view class="title">最新</view>
        <view class="kcbox">
            <ex-list ref="orderListRef" :options="query" :on-form-search="doSearch">
                <template v-slot="{ row }">
                    <view class="kcone" @click="godetail(row.id)">
                        <view class="bm" :style="row.statusArr.name == '报名中' ? 'background-color: #25A3DD' : 'background-color: #AAAAAA'">
                            {{ row.statusArr.name }}
                        </view>
                        <image style="width: 100%; height: 434rpx; border-radius: 10rpx" :src="getImageUrl(row.index_pic)" mode="aspectFill" />
                        <text class="title1">{{ row.title }}</text>
                        <text class="time">培训时间：{{ row.studyStart_time }} ~ {{ row.studyEnd_time }}</text>
                        <view class="ms text-ellipsis-2">{{ row.content || '暂无介绍' }}</view>
                        <view class="bottompart">
                            <view class="class">
                                {{ row.course_role_name }}
                            </view>
                            <view class="more">查看更多 >></view>
                        </view>
                    </view>
                </template>
            </ex-list>
        </view>
        <ex-tabbar current="course"></ex-tabbar>
    </view>
</template>

<style scoped lang="scss">
.course {
    padding: 30rpx 30rpx 160rpx;
}

.title {
    margin: 36rpx 0;
    font-family: Source Han Sans;
    font-size: 32rpx;
    font-weight: normal;
    line-height: normal;
    letter-spacing: normal;
    color: #ffffff;
}

.cardlist {
    .cardone {
        box-sizing: border-box;
        width: calc((100% - 100rpx) / 5);
        padding: 12rpx 8rpx;
        margin-right: 20rpx;
        margin-bottom: 18rpx;
        flex-shrink: 0;

        &:nth-child(5n) {
            margin-right: 0;
        }

        image {
            width: 80rpx;
            height: 80rpx;
            margin-bottom: 6rpx;
        }

        view {
            text-align: center;
            width: 100%;
            font-size: 24rpx;
            color: #ffffff;
        }

        &:last-child {
            margin-right: 0;
        }

        &.card_act {
            border-radius: 20rpx;
            background-color: #fff;

            view {
                font-weight: 700;
                color: #0e2942;
            }
        }
    }
}

.kcbox {
    box-sizing: border-box;

    .kcone {
        display: grid;
        margin-bottom: 60rpx;
        position: relative;

        .bm {
            padding: 4rpx 12rpx;
            border-radius: 90rpx;
            position: absolute;
            top: 10rpx;
            left: 10rpx;
            font-size: 28rpx;
            color: #fff;
        }

        .title1 {
            margin-top: 32rpx;
            font-family: Source Han Sans;
            font-size: 28rpx;
            font-weight: normal;
            line-height: normal;
            letter-spacing: normal;
            color: #ffffff;
        }

        .time {
            margin-top: 8rpx;
            font-family: Source Han Sans;
            font-size: 24rpx;
            font-weight: normal;
            line-height: normal;
            letter-spacing: normal;
            color: #ffffff;
        }

        .ms {
            margin-top: 6rpx;
            font-size: 24rpx;
            color: #ffffff;
        }

        .bottompart {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-top: 18rpx;

            .class {
                padding: 20rpx;
                height: 42rpx;
                border-radius: 40rpx;
                background-color: #ffffff;
                font-family: Source Han Sans;
                font-size: 26rpx;
                font-weight: normal;
                line-height: normal;
                text-align: center;
                display: flex;
                align-items: center;
                justify-content: center;
                letter-spacing: normal;
                color: #25a3dd;
            }

            .more {
                font-family: Source Han Sans;
                font-size: 28rpx;
                font-weight: normal;
                line-height: normal;
                letter-spacing: normal;
                color: #25a3dd;
            }
        }
    }
}
</style>
