<script setup lang="ts">
import exTabbar from '@/components/ex-tabbar/ex-tabbar.vue'
import { bannerList, article } from '@/api/index/index'
import { getCourse, courseIndex } from '@/api/course/index'
import { coachList } from '@/api/coach/index'
import { organization } from '@/api/institution/index'
import { getPlainText, getHeaderImage, getImageUrl } from '@/utils/common'
import informationItem from '@/components/information-item/information-item.vue'
import useUserStore from '@/store/user'
import shareManager from '@/utils/shareManager'
import ShareButton from '@/components/share-button/share-button.vue'

const baseurl = import.meta.env.VITE_APP_BASE_URL
const keyword = ref('')

const noticeid = ref('')
const noticetext = ref('')

// 用户状态管理
const userStore = useUserStore()

// 分享推荐区域显示控制
const showShareArea = ref(true)

// 计算是否显示分享区域（只有登录用户才显示）
const shouldShowShareArea = computed(() => {
    return showShareArea.value && userStore.checkLogin
})

// 创建响应式数据
const tabsList = reactive([
    {
        id: 1,
        name: '推荐'
    },
    // {
    //     id: 2,
    //     name: '热门课程'
    // },
    // {
    //     id: 3,
    //     name: '热门活动'
    // },
    {
        id: 4,
        name: '资讯'
    },
    {
        id: 5,
        name: '教练展示'
    }
])

const tabcurrent = ref(0)
const currentIndex = ref(0)
const current = ref(0)
const swiperList = ref<any[]>([])

const rmList = ref<any[]>([])
const coachData = ref<any[]>([])
const organizationData = ref<any[]>([])

const type = ref(1)

// 定义方法
function tabclick(item: any) {
    current.value = item.index
    keyword.value = ''
    if (item.id === 1) {
        // uni.switchTab({
        // 	url: '/pages/index/index'
        // })
        type.value = 1
        currentIndex.value = 0
        // let params1 = {
        //     title: '',
        //     category_id: '',
        //     page: 1,
        //     limit: 3
        // }
        courseIndex()
            .then((res: any) => {
                // 安全检查：确保数据存在且为数组
                const courseData = res?.data?.data || res?.data || []
                if (Array.isArray(courseData) && courseData.length > 0) {
                    courseData.forEach((e: any) => {
                        if (e && e.content) {
                            e.content = getPlainText(e.content)
                        }
                    })
                }
                rmList.value = courseData
            })
            .catch((error) => {
                console.error('获取课程数据失败:', error)
                rmList.value = []
            })
    }
    // else if (item.id === 2) {
    // 	uni.switchTab({
    // 		url: '/pages/course/index'
    // 	})
    // 	type.value = 1
    // } else if (item.id === 3) {
    // 	uni.switchTab({
    // 		url: '/pages/activity/index'
    // 	})
    // 	type.value = 2
    // }
    else if (item.id === 4) {
        currentIndex.value = 1
        jlListRef.value.refreshFn()
    } else if (item.id === 5) {
        currentIndex.value = 2
        zxListRef.value.refreshFn()
    }
}

const swiperchange = (e: any) => {
    current.value = e.current
}

const swiperclick = (e: any) => {
    // 安全检查：确保轮播图数据存在
    const currentItem = swiperList.value[e]
    if (!currentItem) {
        console.warn('轮播图数据不存在，索引:', e)
        return
    }

    const { target_type, id, target_id } = currentItem

    if (target_type === 1) {
        uni.navigateTo({
            url: '/subPages/index/bannerDetail?id=' + id
        })
    } else if (target_type === 2) {
        uni.navigateTo({
            url: '/subPages/class/classDetail?id=' + target_id
        })
    } else if (target_type === 3) {
        uni.navigateTo({
            url: '/subPages/shop/shopDetail?id=' + target_id
        })
    } else if (target_type === 4) {
        uni.navigateTo({
            url: '/subPages/active/activeDetail?id=' + target_id
        })
    }
}

const gonoticeDetail = () => {
    uni.navigateTo({
        url: '/subPages/index/notice?id=' + noticeid.value
    })
}

// const gomorekc = () => {
//     if (type.value === 1) {
//         uni.switchTab({
//             url: '/pages/course/index'
//         })
//     } else {
//         uni.switchTab({
//             url: '/pages/activity/index'
//         })
//     }
// }

const gojlrz = () => {
    uni.navigateTo({
        url: '/subPages/certification/index'
    })
}

const gozs = () => {
    uni.navigateTo({
        url: '/subPages/certificate/index'
    })
}

const jlListRef = ref()
const zxquery = ref({ title: '', type: '2' })

const zxdoSearch = (formData: { page: number; limit: number }, onSuccess: Function) => {
    const submitData = { ...formData }
    article(submitData).then((res) => {
        const { data } = res as { data: { data: any; total: number } }
        onSuccess({ data })
    })
}

const zxListRef = ref()
const jlquery = ref({ name: '' })

const jldoSearch = (formData: { page: number; limit: number }, onSuccess: Function) => {
    const submitData = { ...formData }
    coachList(submitData).then((res) => {
        const { data } = res as { data: { data: any; total: number } }
        onSuccess({ data })
    })
}

const gojldetail = (id: string) => {
    uni.navigateTo({
        url: '/subPages/index/coachDetail?id=' + id
    })
}

const gojglb = () => {
    uni.navigateTo({
        url: '/subPages/index/institutionList'
    })
}

const gojgdetail = (id: string) => {
    uni.navigateTo({
        url: '/subPages/index/institutionDetail?id=' + id
    })
}

const gokcdetail = (id: string) => {
    uni.navigateTo({
        url: '/subPages/class/classDetail?id=' + id
    })
}

const indexchange = (e: any) => {
    currentIndex.value = e.detail.current
    tabcurrent.value = e.detail.current
}

const search = uni.$util.debounce(() => {
    if (tabcurrent.value === 0) {
        let params1 = {
            title: keyword.value,
            category_id: '',
            page: 1,
            limit: 3
        }
        getCourse(params1)
            .then((res: any) => {
                // 安全检查：确保搜索结果数据存在
                const searchData = res?.data?.data || []
                if (Array.isArray(searchData) && searchData.length > 0) {
                    searchData.forEach((e: any) => {
                        if (e && e.content) {
                            e.content = getPlainText(e.content)
                        }
                    })
                }
                rmList.value = searchData
            })
            .catch((error) => {
                console.error('搜索课程失败:', error)
                rmList.value = []
            })
    } else if (tabcurrent.value === 1) {
        zxquery.value.title = keyword.value
        zxListRef.value.refreshFn()
    } else if (tabcurrent.value === 2) {
        jlquery.value.name = keyword.value
        jlListRef.value.refreshFn()
    }
})

const changetype = (type: number | undefined) => {
    if (!type) return '详情'

    switch (type) {
        case 1:
            return '详情'
        case 2:
            return '课程'
        case 3:
            return '商品'
        case 4:
            return '活动'
        default:
            return '详情'
    }
}

/**
 * 处理分享参数
 */
const handleShareParams = async () => {
    try {
        // 检查是否有分享参数且未处理
        if (shareManager.hasShareParams() && !shareManager.isParamsProcessed()) {
            const shareParams = shareManager.getShareParams()
            console.log('首页处理分享参数:', shareParams)

            // 获取推荐人ID
            const referrerId = shareManager.getReferrerId()
            if (referrerId) {
                // 将推荐人ID保存到缓存中
                uni.setStorageSync('referrer_pid', referrerId)
                console.log('保存推荐人ID到缓存:', referrerId)

                // 如果用户已登录，可以立即调用登录API更新推荐关系
                if (userStore.checkLogin) {
                    await updateReferrerRelation(referrerId)
                } else {
                    // 用户未登录，显示提示信息
                    uni.showToast({
                        title: '检测到推荐链接，请先登录',
                        icon: 'none',
                        duration: 2000
                    })
                }
            }

            // 标记参数已处理
            shareManager.markAsProcessed()
        }
    } catch (error) {
        console.error('处理分享参数失败:', error)
    }
}

/**
 * 更新推荐关系
 */
const updateReferrerRelation = async (referrerId: string) => {
    try {
        // 这里可以调用API更新推荐关系
        // 例如：await updateReferrer({ pid: referrerId })
        console.log('更新推荐关系:', referrerId)

        uni.showToast({
            title: '推荐关系建立成功',
            icon: 'success',
            duration: 2000
        })

        // 清除缓存中的推荐人ID（可选）
        uni.removeStorageSync('referrer_pid')
    } catch (error) {
        console.error('更新推荐关系失败:', error)
    }
}

onShow(async () => {
    try {
        // 处理分享参数（在数据加载前处理）
        await handleShareParams()

        // 重置轮播图数据
        swiperList.value = []

        // 获取轮播图数据
        await bannerList('3')
            .then((res: any) => {
                const bannerData = res?.data || []
                if (Array.isArray(bannerData)) {
                    bannerData.forEach((ele: any) => {
                        if (ele && ele.pic_path) {
                            ele.pic_path = baseurl + ele.pic_path
                            swiperList.value.push(ele)
                        }
                    })
                }
            })
            .catch((error) => {
                console.error('获取轮播图失败:', error)
            })

        // 获取通知数据
        let params = {
            type: 1,
            page: 1,
            limit: 1
        }
        await article(params)
            .then((res: any) => {
                const noticeData = res?.data?.data || []
                if (Array.isArray(noticeData) && noticeData.length > 0 && noticeData[0]) {
                    const firstNotice = noticeData[0]
                    noticetext.value = (firstNotice.title || '') + '：' + getPlainText(firstNotice.content || '')
                    noticeid.value = firstNotice.id || ''
                } else {
                    noticetext.value = '暂无通知'
                    noticeid.value = ''
                }
            })
            .catch((error) => {
                console.error('获取通知失败:', error)
                noticetext.value = '获取通知失败'
                noticeid.value = ''
            })

        // 获取课程数据
        await courseIndex()
            .then((res: any) => {
                const courseData = res?.data || []
                if (Array.isArray(courseData)) {
                    courseData.forEach((e: any) => {
                        if (e && e.content) {
                            e.content = getPlainText(e.content)
                        }
                    })
                    rmList.value = courseData
                } else {
                    rmList.value = []
                }
            })
            .catch((error) => {
                console.error('获取课程数据失败:', error)
                rmList.value = []
            })

        // 获取教练数据
        let params2 = {
            name: '',
            page: 1,
            limit: 5
        }
        await coachList(params2)
            .then((res: any) => {
                const coachListData = res?.data?.data || []
                coachData.value = Array.isArray(coachListData) ? coachListData : []
            })
            .catch((error) => {
                console.error('获取教练数据失败:', error)
                coachData.value = []
            })

        // 获取机构数据
        await organization()
            .then((res: any) => {
                const orgData = res?.data || []
                organizationData.value = Array.isArray(orgData) ? orgData : []
            })
            .catch((error) => {
                console.error('获取机构数据失败:', error)
                organizationData.value = []
            })
    } catch (error) {
        console.error('页面数据加载失败:', error)
        uni.showToast({
            title: '数据加载失败',
            icon: 'none',
            duration: 2000
        })
    }
})
</script>
<template>
    <view class="index">
        <ex-header title="LNT(中国)" background-color="#0e2942" text-color="#fff">
            <template #left>
                <image style="width: 160rpx" src="@/static/img/icon1.png" mode="aspectFill"></image>
            </template>
            <template #right>
                <ShareButton title="LNT(中国) - 专业培训平台" path="/pages/index/index" iconSrc="/static/share.png"
                    :iconStyle="{ width: '44rpx', height: '44rpx' }" :buttonStyle="{ padding: '8rpx' }" />
            </template>
        </ex-header>
        <view class="headpart">
            <view class="searchbox">
                <!-- <image style="width: 46rpx;height: 46rpx;margin-right: 22rpx;" src="@/static/img/sm.png" mode="">
				</image> -->
                <u-search v-model="keyword" bg-color="#f7f8fa" :show-action="false" :placeholder="'请输入搜索关键词'"
                    @change="search"></u-search>
                <image style="width: 48rpx; height: 48rpx; margin-left: 24rpx" src="@/static/img/searrig.png"
                    mode="aspectFill" @click="gozs"></image>
            </view>
            <u-tabs :list="tabsList" @click="tabclick" :current="tabcurrent" line-color="#fff" line-width="30"
                :active-style="{ color: '#fff', fontFamily: 'Source Han Sans', fontSize: '30rpx' }"
                :inactive-style="{ color: '#AAAAAA', fontFamily: 'Source Han Sans', fontSize: '30rpx' }"></u-tabs>
        </view>
        <swiper :current="currentIndex" class="swiper-container" @change="indexchange">
            <swiper-item>
                <scroll-view scroll-y="true">
                    <u-notice-bar :text="noticetext" bg-color="#1A2B4A00" color="#25A3DD"
                        @click="gonoticeDetail"></u-notice-bar>
                    <view class="cardtype">
                        {{ changetype(swiperList[current]?.target_type) }}
                    </view>
                    <u-swiper :list="swiperList" :indicator="true" current="current" indicator-style="bottom"
                        key-name="pic_path" circular radius="0" height="220" @click="swiperclick"
                        @change="swiperchange">
                        <template #indicator>
                            <view class="indicator">
                                <view class="indicator__dot" v-for="(_, index) in swiperList" :key="index"
                                    :class="[index === current && 'indicator__dot--active']"></view>
                            </view>
                        </template>
                    </u-swiper>
                    <view class="rmbox">
                        <text class="title">近期热门</text>
                        <template v-if="rmList.length > 0">
                            <view class="kcone" v-for="(item, index) in rmList" :key="index"
                                @click="gokcdetail(item.id)">
                                <image style="width: 100%; height: 434rpx; border-radius: 10rpx"
                                    :src="getImageUrl(item.index_pic)" mode="aspectFill" />
                                <text class="title1">{{ item.title }}</text>
                                <text class="time">培训时间：{{ item.studyStart_time }} ~ {{ item.studyEnd_time }}</text>
                                <view class="ms text-ellipsis-2">{{ item.content || '暂无介绍' }}</view>
                                <view class="bottompart">
                                    <view class="class">
                                        {{ item.course_role_name }}
                                    </view>
                                    <view class="more">查看更多 >></view>
                                </view>
                            </view>
                        </template>
                        <ex-empty v-else :tips="'暂无数据'" />
                    </view>
                </scroll-view>
            </swiper-item>
            <swiper-item>
                <scroll-view style="padding: 0 10px; box-sizing: border-box" scroll-y="true">
                    <ex-list ref="zxListRef" :options="zxquery" :on-form-search="zxdoSearch">
                        <template v-slot="{ row }">
                            <informationItem :item="row" :is-show-tools="false" />
                        </template>
                    </ex-list>
                </scroll-view>
            </swiper-item>
            <swiper-item>
                <scroll-view style="padding-left: 40rpx; padding-right: 40rpx; box-sizing: border-box; display: grid"
                    scroll-y="true">
                    <view class="flex-center-between">
                        <text class="ntxt">LNT官方认证</text>
                        <view class="inadd flex-center-start" @click="gojlrz">
                            <image style="width: 30rpx; height: 34rpx" src="@/static/inadd.png" mode=""></image>
                            加入我们
                        </view>
                    </view>
                    <view class="rzlist flex-center-start">
                        <view class="rzone" v-for="(item, index) in coachData" :key="index"
                            @click="gojldetail(item.member_id)">
                            <image class="txp" :src="getHeaderImage(item.photo)" mode=""></image>
                            <image class="icon" src="@/static/rzblue.png" mode=""></image>
                            <text class="rzname">{{ item.name }}</text>
                            <view class="cardz">
                                {{ item.member_role_name }}
                            </view>
                        </view>
                    </view>
                    <view class="flex-center-between" style="margin-top: 54rpx">
                        <text class="titlex">权威机构</text>
                        <image class="qwjg" src="@/static/qwjg.png" @click="gojglb" mode=""></image>
                    </view>
                    <view class="jglist">
                        <view class="jgone" v-for="(item, index) in organizationData" :key="index"
                            @click="gojgdetail(item.id)">
                            <image class="picc" :src="getImageUrl(item.image_path)" mode="aspectFill"></image>
                            <view class="rzicon flex-center-start">
                                <image style="width: 26rpx; height: 26rpx; margin-right: 4rpx" src="@/static/qwrx.png"
                                    mode="">
                                </image>
                                认证
                            </view>
                            <view class="titlej text-ellipsis">{{ item.title }}</view>
                        </view>
                    </view>
                    <view style="margin-top: 24rpx">
                        <text class="titlex">全部教练</text>
                        <ex-list ref="jlListRef" :options="jlquery" :on-form-search="jldoSearch">
                            <template v-slot="{ row }">
                                <view class="jllist" @click="gojldetail(row.member_id)">
                                    <image class="img" :src="getHeaderImage(row.photo)" mode=""></image>
                                    <view class="rightp">
                                        <view class="flex-center-start">
                                            <text class="name">{{ row.name }}</text>
                                            <view class="class">{{ row.member_role_name }}</view>
                                        </view>
                                        <text class="name text-ellipsis-1" style="margin-top: 10rpx">{{ row.introduction
                                            }}</text>
                                    </view>
                                </view>
                            </template>
                        </ex-list>
                    </view>
                </scroll-view>
            </swiper-item>
        </swiper>

        <!-- 分享推荐区域 -->
        <view v-if="shouldShowShareArea" class="share-recommend-area">
            <view class="share-content">
                <view class="share-text">
                    <text class="share-title">发现好内容，分享给朋友</text>
                    <text class="share-desc">邀请好友一起学习，共同进步</text>
                </view>
                <view class="share-actions">
                    <ShareButton title="LNT(中国) - 专业培训平台，一起来学习吧！" path="/pages/index/index" :showText="true" text="立即分享"
                        iconSrc="/static/share.png" :buttonStyle="{
                            backgroundColor: '#007aff',
                            color: '#fff',
                            padding: '12rpx 24rpx',
                            borderRadius: '50rpx',
                            display: 'flex',
                            alignItems: 'center',
                            gap: '8rpx'
                        }" :textStyle="{ color: '#fff', fontSize: '28rpx' }"
                        :iconStyle="{ width: '32rpx', height: '32rpx' }" />
                    <view class="close-btn" @tap="showShareArea = false">
                        <text class="close-icon">×</text>
                    </view>
                </view>
            </view>
        </view>

        <ex-tabbar current="home"></ex-tabbar>
    </view>
</template>

<style scoped lang="scss">
.index {
    padding-bottom: 160rpx;

    .headpart {
        padding: 30rpx 30rpx 0 30rpx;

        .searchbox {
            display: flex;
            align-items: center;
        }

        :deep(.u-notice-bar) {
            padding: 0;
        }
    }

    .swiper-container {
        height: 75vh;

        swiper-item {
            height: 100%;
            overflow-y: auto;

            .inadd {
                font-family: Source Han Sans;
                font-size: 28rpx;
                color: #25a3dd;
            }

            .ntxt {
                font-family: Source Han Sans;
                font-size: 32rpx;
                color: #ffffff;
            }

            .rzlist {
                margin-top: 12rpx;
                gap: 2%;

                .rzone {
                    display: grid;
                    align-items: center;
                    justify-items: center;
                }

                .txp {
                    width: 114rpx;
                    height: 114rpx;
                    border-radius: 50%;
                }

                .icon {
                    width: 34rpx;
                    height: 34rpx;
                    position: relative;
                    right: -34rpx;
                    bottom: 34rpx;
                }

                .rzname {
                    font-family: Source Han Sans;
                    font-size: 24rpx;
                    color: #ffffff;
                }

                .cardz {
                    margin-top: 24rpx;
                    padding: 4rpx 15rpx;
                    border-radius: 58rpx;
                    background: #8cc046;
                    font-family: Source Han Sans;
                    font-size: 24rpx;
                    color: #ffffff;
                }
            }

            .titlex {
                font-family: Source Han Sans;
                font-size: 32rpx;
                color: #ffffff;
            }

            .qwjg {
                width: 40rpx;
                height: 40rpx;
            }

            .jglist {
                margin-top: 16rpx;
                display: flex;
                align-items: center;
                justify-content: space-between;

                .jgone {
                    width: 46%;
                    border-radius: 22rpx;
                    background: rgba(255, 255, 255, 0.298);
                    padding: 8rpx 36rpx;
                    display: grid;
                    align-items: center;
                    justify-items: center;

                    .picc {
                        width: 100%;
                        height: 212rpx;
                        border-radius: 20rpx;
                    }

                    .rzicon {
                        font-family: Source Han Sans;
                        font-size: 20rpx;
                        color: #ffffff;
                        border-radius: 14rpx;
                        background: #25a3dd;
                        padding: 5rpx 20rpx;
                        position: relative;
                        left: -64rpx;
                        top: -212rpx;
                    }

                    .titlej {
                        width: 100%;
                        margin-top: -16rpx;
                        font-family: Source Han Sans;
                        font-size: 28rpx;
                        color: #ffffff;
                        max-width: 100%;
                        overflow: hidden;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                    }
                }
            }

            .jllist {
                margin-top: 20rpx;
                width: 100%;
                padding: 20rpx;
                border-radius: 22rpx;
                background: rgba(255, 255, 255, 0.298);
                box-sizing: border-box;
                display: flex;
                align-items: center;

                .img {
                    width: 132rpx;
                    height: 132rpx;
                    border-radius: 50%;
                }

                .name {
                    font-family: Source Han Sans;
                    font-size: 28rpx;
                    font-weight: normal;
                    line-height: normal;
                    letter-spacing: normal;
                    color: #ffffff;
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                }

                .rightp {
                    display: grid;
                    align-items: center;
                    margin-left: 36rpx;
                    max-width: 72%;

                    .class {
                        margin-left: 50rpx;
                        padding: 4rpx 10rpx;
                        border-radius: 58rpx;
                        background: #8cc046;
                        font-family: Source Han Sans;
                        font-size: 24rpx;
                        font-weight: normal;
                        line-height: normal;
                        text-align: center;
                        display: flex;
                        align-items: center;
                        letter-spacing: normal;
                        color: #ffffff;
                    }
                }
            }
        }

        .rmbox {
            padding: 26rpx 60rpx;
            display: grid;

            .title {
                font-family: Source Han Sans;
                font-size: 36rpx;
                font-weight: bold;
                line-height: normal;
                letter-spacing: normal;
                color: #ffffff;
            }

            .rmlist {
                width: 100%;
                display: grid;

                .txt {
                    margin-top: 14rpx;
                    margin-left: 14rpx;
                    font-family: Source Han Sans;
                    font-size: 28rpx;
                    font-weight: bold;
                    line-height: normal;
                    letter-spacing: normal;
                    color: #ffffff;
                }

                .time {
                    margin-top: 12rpx;
                    margin-left: 14rpx;
                    font-family: Source Han Sans;
                    font-size: 24rpx;
                    font-weight: normal;
                    line-height: normal;
                    letter-spacing: normal;
                    color: #ffffff;
                }

                .ms {
                    margin-top: 12rpx;
                    margin-left: 12rpx;
                    font-family: Source Han Sans;
                    font-size: 24rpx;
                    font-weight: normal;
                    line-height: normal;
                    letter-spacing: normal;
                    color: #ffffff;
                }

                .rich_text {
                    margin-top: 12rpx;
                    margin-left: 12rpx;
                    font-family: Source Han Sans;
                    font-size: 24rpx;
                    font-weight: normal;
                    line-height: normal;
                    letter-spacing: normal;
                    color: #ffffff;
                }
            }

            .kcone {
                display: grid;
                margin-bottom: 60rpx;
                position: relative;

                .bm {
                    padding: 4rpx 12rpx;
                    border-radius: 90rpx;
                    position: absolute;
                    top: 10rpx;
                    left: 10rpx;
                    font-size: 28rpx;
                    color: #fff;
                }

                .title1 {
                    margin-top: 32rpx;
                    font-family: Source Han Sans;
                    font-size: 28rpx;
                    font-weight: normal;
                    line-height: normal;
                    letter-spacing: normal;
                    color: #ffffff;
                }

                .time {
                    margin-top: 8rpx;
                    font-family: Source Han Sans;
                    font-size: 24rpx;
                    font-weight: normal;
                    line-height: normal;
                    letter-spacing: normal;
                    color: #ffffff;
                }

                .ms {
                    margin-top: 6rpx;
                    font-size: 24rpx;
                    color: #ffffff;
                }

                .bottompart {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    margin-top: 18rpx;

                    .class {
                        padding: 20rpx;
                        height: 42rpx;
                        border-radius: 40rpx;
                        background-color: #ffffff;
                        font-family: Source Han Sans;
                        font-size: 26rpx;
                        font-weight: normal;
                        line-height: normal;
                        text-align: center;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        letter-spacing: normal;
                        color: #25a3dd;
                    }

                    .more {
                        font-family: Source Han Sans;
                        font-size: 28rpx;
                        font-weight: normal;
                        line-height: normal;
                        letter-spacing: normal;
                        color: #25a3dd;
                    }
                }
            }

            .morekc {
                text-align: center;
                margin-top: 60rpx;
                font-family: Source Han Sans;
                font-size: 28rpx;
                font-weight: normal;
                line-height: normal;
                letter-spacing: normal;
                color: #25a3dd;
            }
        }
    }
}

.indicator {
    @include flex(row);
    justify-content: center;
    padding: 8rpx;
    border-radius: 120rpx;
    background: rgba(255, 255, 255, 0.2392);
    position: relative;
    left: -184rpx;
    bottom: 16rpx;

    &__dot {
        width: 52rpx;
        height: 8rpx;
        border-radius: 14rpx;
        background-color: #cacaca;
        margin: 0 10rpx;
        transition: background-color 0.3s;

        &--active {
            background-color: #ffffff;
        }
    }
}

.cardtype {
    z-index: 9;
    top: 50px;
    position: fixed;
    padding: 3px 10px;
    box-sizing: border-box;
    border-radius: 0 10px 10px 0;
    background-color: #000000;
    font-family: Source Han Sans;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
}

// 分享推荐区域样式
.share-recommend-area {
    position: fixed;
    bottom: 160rpx; // 在 tabbar 上方
    left: 0;
    right: 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 24rpx 30rpx;
    margin: 0 20rpx;
    border-radius: 16rpx;
    box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
    z-index: 10;

    .share-content {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .share-text {
            display: flex;
            flex-direction: column;
            flex: 1;

            .share-title {
                color: #fff;
                font-size: 32rpx;
                font-weight: 600;
                margin-bottom: 8rpx;
            }

            .share-desc {
                color: rgba(255, 255, 255, 0.8);
                font-size: 24rpx;
            }
        }

        .share-actions {
            display: flex;
            align-items: center;
            gap: 16rpx;

            .close-btn {
                width: 48rpx;
                height: 48rpx;
                background: rgba(255, 255, 255, 0.2);
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;

                .close-icon {
                    color: #fff;
                    font-size: 36rpx;
                    font-weight: 300;
                    line-height: 1;
                }
            }
        }
    }
}
</style>
