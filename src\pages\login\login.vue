<script setup lang="ts">
import { getOpenId, login } from '@/api/login/index'
import useUserStore from '@/store/user'

const userStore = useUserStore()
const aloneChecked = ref(false)

const showToast = () => {
    uni.showToast({ title: '请勾选LNT（中国）用户协议', icon: 'none', mask: true })
}

const loginByPhone = ({ target }: any) => {
    const { errMsg: err, code: codes } = target
    if (err === 'getPhoneNumber:ok') {
        uni.login({
            provider: 'weixin', //使用微信登录
            success: async function (loginRes) {
                const { code, errMsg } = loginRes
                if (errMsg === 'login:ok') {
                    const loginRes = (await getOpenId({ code })) as { code: number; data: string }
                    if (loginRes.code === 1) {
                        uni.showLoading({ title: '登录中' })
                        console.log(loginRes)

                        const loginData: { code: string; openid: string; pid?: string } = {
                            code: codes,
                            openid: loginRes.data[0]
                        }
                        login(loginData).then(async (res) => {
                            const { code, data } = res as {
                                code: number
                                data: { token: string }
                            }
                            if (code === 1) {
                                userStore.token = data.token

                                uni.showToast({ title: '登录成功', icon: 'none', mask: true })
                                userStore.getUserInfo().then(() => {
                                    uni.getUserInfo({
                                        provider: 'weixin',
                                        success: (res) => {
                                            const { userInfo } = res
                                            userStore.userInfo.name = userInfo.nickName
                                            userStore.userInfo.avatar = userInfo.avatarUrl
                                        }
                                    })
                                    setTimeout(() => {
                                        uni.navigateBack()
                                    }, 1500)
                                })
                                await userStore.getMemberLevelList()
                            }
                        })
                    }
                }
            }
        })
    }
}

const goBack = () => {
    uni.switchTab({ url: '/pages/index/index' })
}

const showAgreement = () => {
    uni.navigateTo({
        url: '/subPages/agreement/index'
    })
}
</script>
<template>
    <view class="flex column login">
        <ex-header title="手机号快捷登录" background-color="#1a2b4a" text-color="#fff" :on-goback="goBack" mode="dark" />
        <view class="login-logo">
            <image src="@/static/loginlogo.png" mode="widthFix" />
            <view class="title">LNT(中国)</view>
        </view>
        <view class="logo-btn-box">
            <u-checkbox
                :custom-style="{ marginRight: '8px' }"
                name="agree"
                used-alone
                v-model:checked="aloneChecked"
                active-color="#70b603"
                size="28rpx"
            >
                <template v-slot:label>
                    <view class="desc">
                        我已同意并阅读
                        <text @tap="showAgreement">《LNT（中国）用户协议》</text>
                    </view>
                </template>
            </u-checkbox>
            <button v-if="aloneChecked" open-type="getPhoneNumber" @getphonenumber="loginByPhone" class="login-button">快速登录</button>
            <button v-else class="login-button" @tap="showToast">快速登录</button>
        </view>
    </view>
</template>

<style scoped lang="scss">
.login {
    width: 100vw;
    height: 100vh;
    background-color: #1a2b4a;

    &-bg {
        position: fixed;
        top: 0;
        left: 0;
        z-index: -1;
    }

    &-logo {
        margin-bottom: 36vh;

        image {
            width: 160rpx;
        }

        .title {
            color: #fff;
            font-size: 36rpx;
            margin-top: 20rpx;
        }
    }

    .desc {
        font-size: 28rpx;
        color: #fff;

        text {
            color: #25a3dd;
        }
    }

    &-button {
        background-color: #000000;
        color: #fff;
        height: fit-content;
        border-radius: 90rpx;
        padding: 20rpx 40rpx;
        margin-top: 20rpx;
    }
}
</style>
