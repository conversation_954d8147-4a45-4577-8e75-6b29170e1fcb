<script setup lang="ts">
import exTabbar from '@/components/ex-tabbar/ex-tabbar.vue'
import useUserStore from '@/store/user'
import { getHeaderImage } from '@/utils/common'
import { useShare } from '@/composables/useShare'

// 使用全局分享功能 - 我的页面跳转到首页
const { handleShareParams, getShareAppMessageConfig, getShareTimelineConfig } = useShare({
    title: 'LNT(中国) - 加入我们，一起学习成长！',
    imageUrl: '/static/logo.png',
    path: '/pages/mine/index',
    redirectToHome: true // 我的页面分享后跳转到首页
})

// 设置分享给朋友功能
onShareAppMessage(() => {
    return getShareAppMessageConfig()
})

// 设置分享到朋友圈功能
onShareTimeline(() => {
    return getShareTimelineConfig()
})
const userStore = useUserStore()
const navto = (url: string, params = {}) => uni.$util.goToPage({ url, params })

const itemList = ref([
    {
        name: '课程报名',
        icon: '/static/kc.png',
        path: 'subPages/course/index'
    },
    {
        name: '商品订单',
        icon: '/static/dd.png',
        path: ''
    },
    {
        name: '我的证书',
        icon: '/static/zs.png',
        path: 'subPages/certificate/index'
    },
    {
        name: '活动报名',
        icon: '/static/bm.png',
        path: 'subPages/active/orderList'
    },
    {
        name: '完善信息',
        icon: '/static/xx.png',
        path: 'subPages/index/improveInformation'
    },
    {
        name: '认证',
        icon: '/static/rz.png',
        path: 'subPages/certification/index'
    },
    {
        name: '资讯',
        icon: '/static/zx.png',
        path: 'subPages/Information/index'
    },
    {
        name: '关于我们',
        icon: '/static/wm.png',
        path: 'subPages/aboutUs/index'
    },
    {
        name: '客服中心',
        icon: '/static/zx.png',
        path: ''
    }
])

// const info = ref(userStore.userInfo)
// const lookEquity = () => {
//     console.log(111)
// }

const goLogin = uni.$util.throttle((type: boolean) => {
    if (!userStore.checkLogin) {
        console.log('登录')
        navto('pages/login/login')
        if (type) return
    } else {
        uni.showModal({
            title: '提示',
            content: '确认退出登录吗？',
            success: (res) => {
                if (res.confirm) {
                    userStore.logOut()
                    uni.showToast({
                        title: '退出登录成功',
                        icon: 'none',
                        mask: true
                    })
                }
            }
        })
    }
})

const list: any = computed(() => {
    // 装修模式
    getMemberLevelFn(userStore.memberLevelList)
    return userStore.memberLevelList
})

const info: any = computed(() => {
    return userStore.userInfo || {}
})

const upgradeGrowth = ref(0) // 升级下级会员所需的成长值
const currIndex = ref(0) //当前会员索引
const afterCurrIndex = ref(-1) // 下一个会员等级索引
const getMemberLevelFn = (list: any) => {
    if (!list || !list.length) return false
    let isSet = false
    // 刚进来处理会员等级数据
    if (info.value && info.value.member_level && list && list.length) {
        list.forEach((item: any, index: any) => {
            if (item.level_id === info.value.member_level) {
                currIndex.value = index + 1
            }
            if (item.growth > info.value.growth && item.level_id !== info.value.member_level && !isSet) {
                afterCurrIndex.value = index
                isSet = true
            }
        })
    }

    if (info.value.member_level) {
        if (afterCurrIndex.value === -1) {
            afterCurrIndex.value = list.length - 1
        }

        if (list[afterCurrIndex.value] && list[afterCurrIndex.value].growth) {
            upgradeGrowth.value = list[afterCurrIndex.value].growth - info.value.growth
        }
    } else {
        // 当前会员没有会员等级，则展示会员等级中的最后一个等级
        info.value.member_level_name = list[0].level_name
        upgradeGrowth.value = list[0].growth - (info.value.growth || 0)
        afterCurrIndex.value = 0
        currIndex.value = 1
    }
}

// 进度条值
const progress = () => {
    let num = 100
    if (list.value[afterCurrIndex.value] && list.value[afterCurrIndex.value].growth) {
        if (info.value.growth) {
            num = (info.value.growth / list.value[afterCurrIndex.value].growth) * 100
        } else {
            num = 0
        }
    }
    return num
}

onShow(async () => {
    // 处理分享参数（推荐人逻辑）
    await handleShareParams()

    if (userStore.checkLogin) {
        await userStore.getUserInfo()
        await userStore.getMemberLevelList()
    }
})
</script>
<template>
    <view class="mine">
        <ex-header title="我的" background-color="#0e2942" text-color="#fff">
            <template #left>
                <image style="width: 160rpx" src="@/static/img/icon1.png" mode="aspectFill" />
            </template>
        </ex-header>
        <view class="content">
            <view class="login" v-if="userStore.checkLogin">
                <image class="bg" src="@/static/mine_bac.png" mode="widthFix" />
                <view class="flex-start-start user_box">
                    <ex-image :src="getHeaderImage(userStore.userInfo.headimg)" width="160rpx" height="160rpx"
                        :is-icon="true" />
                    <view class="user_det flex1">
                        <view class="flex-start-between name_box">
                            <view class="left flex1">
                                <text>{{ userStore.userInfo.nickname }}</text>
                                <image v-if="userStore.userInfo.member_level != 0" class="vip" src="@/static/v.png" />
                            </view>
                            <view class="right" @tap="navto('subPages/businessCard/index')">修改资料</view>
                        </view>
                        <view class="introduction text-ellipsis-2">{{ userStore.userInfo.introduction || '暂无介绍' }}
                        </view>
                        <view class="flex-start-start tags_box wrap">
                            <view v-show="userStore.userInfo.member_level_name" class="flex tags">
                                <image src="@/static/vip.png" />
                                <text>{{ userStore.userInfo.member_level_name || '普通用户' }}</text>
                            </view>
                            <view v-show="userStore.userInfo.growth" class="flex tags">
                                <image src="@/static/jf.png" />
                                <text>{{ userStore.userInfo.growth }}积分</text>
                            </view>
                            <view class="flex tags">
                                <image src="@/static/pz.png" />
                                <text>跑者</text>
                            </view>
                            <view class="flex tags" v-if="userStore.userInfo.create_time">
                                <image src="@/static/jr.png" />
                                <text>{{ userStore.userInfo.create_time.substring(0, 10) }}加入LNT(中国)</text>
                            </view>
                        </view>
                    </view>
                </view>
                <view class="equity_box">
                    <image class="back" src="@/static/mineqy.png" mode="widthFix" />
                    <view class="flex-center-start title_box">
                        <image src="@/static/vip.png" />
                        <text>{{ userStore.userInfo.member_level_name || '普通用户' }}</text>
                    </view>
                    <view class="flex-center-between center_box">
                        <view class="left">{{ userStore.userInfo.growth || 0 }}积分</view>
                        <!-- <view class="right" @tap="lookEquity">查看我的权益 ></view> -->
                    </view>
                    <view class="progress" v-if="list.length > 0">
                        <u-line-progress :percentage="progress()" active-color="#1a2b4a" inactive-color="#e5e5e5"
                            height="4" :show-text="false" />
                    </view>
                </view>
            </view>
            <view class="not_login" v-else>
                <view class="photo_box" @tap="goLogin(true)">
                    <image src="@/static/not_login.png" />
                    <text>登录/注册</text>
                </view>
                <view class="flex vip_box" @tap="goLogin(true)">
                    <view class="flex column left_box">
                        <image src="@/static/vip.png" />
                        <view class="left_desc">LNT会员</view>
                    </view>
                    <view class="flex1 right_desc">登录查看更多会员权益</view>
                </view>
            </view>
            <view class="item_box">
                <view v-for="(v, k) in itemList" :key="k" @tap="navto(v.path)">
                    <button v-if="v.name === '客服中心'" class="flex column item" open-type="contact"
                        bindcontact="handleContact" session-from="sessionFrom">
                        <image :src="v.icon" />
                        <text>{{ v.name }}</text>
                    </button>
                    <view v-else class="flex column item">
                        <image :src="v.icon" />
                        <text>{{ v.name }}</text>
                    </view>
                </view>
                <view v-if="userStore.userInfo.is_vertify === 1" class="flex column item"
                    @tap="navto('subPages/writeOff/index')">
                    <image src="@/static/sys.png" />
                    <text>扫码核销</text>
                </view>
            </view>
            <view class="login_btn" @tap="goLogin(false)">{{ userStore.checkLogin ? '退出登录' : '一键登录' }}</view>
        </view>
        <ex-tabbar current="mine"></ex-tabbar>
    </view>
</template>

<style scoped lang="scss">
.mine {
    padding-bottom: 150rpx;

    .login {
        position: relative;

        .bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            opacity: 0.9;
            z-index: -1;
        }

        .user_box {
            padding: 120rpx 30rpx 38rpx;

            :deep(.ex-images) {
                image {
                    border-radius: 50% !important;
                }

                margin-right: 34rpx;
            }

            .user_det {
                .left {
                    text {
                        font-weight: 700;
                        font-size: 32rpx;
                        color: #ffffff;
                        vertical-align: middle;
                    }

                    image {
                        vertical-align: middle;
                        margin-left: 20rpx;
                        width: 40rpx;
                        height: 40rpx;
                    }
                }

                .right {
                    font-size: 28rpx;
                    color: #d7d7d7;
                }
            }

            .introduction {
                margin: 24rpx 0;
                font-size: 24rpx;
                color: #ffffff;
            }

            .tags_box {
                .tags {
                    margin-right: 6rpx;
                    margin-bottom: 6rpx;
                    background-color: #ffc500;
                    border-radius: 90rpx;
                    border: 4rpx solid #1a2b4a;
                    padding: 6rpx 16rpx;

                    image {
                        margin-right: 6rpx;
                        width: 24rpx;
                        height: 24rpx;
                    }

                    text {
                        font-size: 24rpx;
                        color: #1a2b4a;
                    }
                }
            }
        }

        .equity_box {
            position: relative;
            width: 90vw;
            margin: 0 auto;
            padding: 30rpx;
            background-color: #8cc046;
            border-radius: 16rpx;
            text-align: center;
            overflow: hidden;

            .back {
                position: absolute;
                width: 400rpx;
                right: 20rpx;
                bottom: 0;
                z-index: 1;
            }

            .title_box {
                position: relative;
                z-index: 2;

                image {
                    width: 40rpx;
                    height: 40rpx;
                    margin-right: 16rpx;
                }

                text {
                    font-weight: 700;
                    font-size: 28rpx;
                    color: #1a2b4a;
                }
            }

            .center_box {
                position: relative;
                z-index: 2;
                margin: 16rpx 0 30rpx;
                font-weight: 700;
                font-size: 24rpx;
                color: #1a2b4a;

                .left {
                    font-size: 32rpx;
                }
            }

            .progress {
                position: relative;
                z-index: 2;
            }
        }
    }

    .not_login {
        padding: 30rpx;

        .photo_box {
            image {
                width: 160rpx;
                height: 160rpx;
                vertical-align: middle;
                margin-right: 30rpx;
            }

            text {
                vertical-align: middle;
                font-size: 32rpx;
                color: #fff;
            }
        }

        .vip_box {
            margin-top: 48rpx;
            padding: 24rpx;
            background-color: #8cc046;
            border-radius: 16rpx;

            .left_box {
                padding-right: 30rpx;
                position: relative;

                &::before {
                    content: '';
                    position: absolute;
                    width: 2rpx;
                    height: 100%;
                    background-color: #1a2b4a;
                    right: 0;
                    top: 0;
                }

                image {
                    width: 40rpx;
                    height: 40rpx;
                }

                .left_desc {
                    font-size: 24rpx;
                    color: #1a2b4a;
                }
            }

            .right_desc {
                margin-left: 30rpx;
                font-size: 28rpx;
                color: #1a2b4a;
            }
        }
    }

    .item_box {
        padding: 30rpx;
        margin-top: 40rpx;
        display: grid;
        grid-template-columns: repeat(5, 1fr);
        gap: 30rpx;

        .item {
            image {
                width: 52rpx;
                height: 52rpx;
                margin-bottom: 16rpx;
            }

            text {
                font-size: 24rpx;
                color: #fff;
            }
        }
    }

    .login_btn {
        box-sizing: border-box;
        width: 90vw;
        position: fixed;
        bottom: 180rpx;
        left: 0;
        right: 0;
        margin: 0 auto;
        padding: 20rpx 0;
        border-radius: 24rpx;
        background: #ffffff;
        font-size: 32rpx;
        text-align: center;
        color: #1a2b4a;
    }
}
</style>
