<script setup lang="ts">
import { signDetail, cancelSign } from '@/api/active/index'
// activityDetail
// import { payInfo } from '@/api/common/common'
// getSevenDaysBeforeCutoff,
import { getImageUrl } from '@/utils/common'
import { onLoad } from '@dcloudio/uni-app'

// const detaildata = ref({})
const details = ref<any>({})
const getDetail = (id: string) => {
    signDetail(id).then((res) => {
        const { data } = res as { data: { [n: string]: string } }
        console.log(data)
        details.value = data
    })
}

const qxbm = async () => {
    await cancelSign({ id: details.value.id }).then((res: any) => {
        if (res.code === 1) {
            uni.showModal({
                content: '取消成功',
                success: (res) => {
                    if (res.confirm) {
                        uni.navigateTo({
                            url: '/subPages/active/orderList'
                        })
                    } else if (res.cancel) {
                        console.log('用户取消')
                    }
                }
            })
        }
    })
}

onLoad((opt) => {
    getDetail(opt.id)
})
</script>
<template>
    <view class="order_detail">
        <ex-header title="报名详情" background-color="#1a2b4a" text-color="#fff" mode="dark" />
        <view class="content">
            <view class="flex-start-start top_box">
                <image :src="getImageUrl(details.pic_path)" mode="aspectFill" />
                <view class="flex1 order_tit">
                    <view class="tit">{{ details.title }}</view>
                </view>
            </view>
        </view>
        <view class="content">
            <view class="bot_box">
                <view class="items">活动时间：{{ details.activity_start_time }} ~ {{ details.activity_end_time }}</view>
                <view class="items">活动地点：{{ details.address || '暂无' }}</view>
            </view>
        </view>
        <view class="content">
            <text class="title" style="font-size: 36rpx; margin-bottom: 36rpx">报名信息</text>
            <view class="titles flex-center-between">
                <text class="title">手机号码</text>
                <text class="title">{{ details.mobile }}</text>
            </view>
            <view class="titles flex-center-between">
                <text class="title">姓名</text>
                <text class="title">{{ details.name }}</text>
            </view>
            <view class="titles flex-center-between">
                <text class="title">报名时间</text>
                <text class="title">{{ details.create_time }}</text>
            </view>
            <view class="titles flex-center-between">
                <text class="title">参与状态</text>
                <text class="title">{{ details.status_name }}</text>
            </view>
        </view>
        <view class="content">
            <view class="flex-center-between">
                <text class="title">活动二维码{{ details.status === 1 ? '：入场' : details.status === 2 ? '：退场' : '' }}</text>
                <text class="title" v-if="details.status === 3 || details.status === 10">已过期</text>
            </view>
            <u-qrcode
                v-if="details.status === 1 || details.status === 2"
                :size="160"
                :val="details.verification_code"
                show-loading
                loading-text="loading..."
            ></u-qrcode>
            <view class="txt" v-if="details.status === 1 || details.status === 2">活动现场扫码核销获取20积分</view>
        </view>
        <view class="content" v-if="details.status === 3">
            <view class="txt">已成功签退 积分+20</view>
        </view>
        <u-button v-if="details.status === 1 || details.status === 2" :disabled="details.status === 2" @click="qxbm">取消报名</u-button>
        <u-button v-if="details.status === 3 || details.status === 10" disabled="false">已结束</u-button>
    </view>
</template>

<style scoped lang="scss">
.order_detail {
    padding: 30rpx;

    .content {
        border: 2rpx solid #535f75;
        background-color: #2f3e5a;
        border-radius: 16rpx;
        padding: 30rpx;
        margin-bottom: 30rpx;
        font-size: 28rpx;
        color: #ffffff;
        display: grid;

        &:last-child {
            margin-bottom: 0;
        }

        .top_box {
            image {
                border-radius: 8rpx;
                width: 194rpx;
                height: 132rpx;
                margin-right: 16rpx;
            }

            .order_tit {
                word-break: break-all;
                font-size: 28rpx;
                color: #ffffff;
            }

            .price {
                margin-top: 26rpx;
                font-size: 32rpx;
                color: #fff;
            }
        }

        .bot_box {
            .items {
                margin-top: 20rpx;
                font-size: 28rpx;
                color: #ffffff;
            }
        }

        .titles {
            margin-top: 14rpx;

            .title {
                font-size: 28rpx;
                color: #ffffff;
            }
        }

        .growth {
            text {
                font-size: 24rpx;
                color: #ffffff;
            }

            image {
                margin-left: 20rpx;
                width: 30rpx;
                height: 30rpx;
            }
        }

        .txt {
            font-family: Source Han Sans;
            font-size: 24rpx;
            font-weight: normal;
            line-height: normal;
            letter-spacing: normal;
            color: #ffffff;
        }

        :deep(.u-qrcode) {
            display: flex;
            justify-content: center;
        }
    }

    :deep(.u-button) {
        width: 90vw;
        position: fixed;
        bottom: 20rpx;
        left: 0;
        right: 0;
        margin: 0 auto;
        border-radius: 90rpx;
        color: #1a2b4a;
    }
}
</style>
