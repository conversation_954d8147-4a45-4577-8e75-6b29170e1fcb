<template>
    <ex-header title="通知" background-color="#1a2b4a" text-color="#fff" mode="dark" />
    <view class="classbox">
        <view class="noticeone" v-for="(item, index) in noticeList" :key="index">
            <text class="txt">【通知】{{ item.title }}</text>
            <rich-text class="time" :nodes="item.content"></rich-text>
            <text class="time">发布时间：{{ item.create_time }}</text>
        </view>
    </view>
</template>

<script setup lang="ts">
import { articleDetail } from '@/api/index/index'

const noticeList = ref<any[]>([])

onLoad(async (e: any) => {
    await articleDetail(e.id).then((res: any) => {
        noticeList.value.push(res.data)
    })
    noticeList.value.forEach((ele) => {
        ele.content = ele.content.replace(/<img/g, `<img style="width:100%; max-width: 100%;"`)
            .replace(/<p([^>]*)>/g, function (match, p1) {
                if (/style\s*=\s*['"][^'"]*background-color\s*:[^'"]*['"]/i.test(p1)) {
                    // 如果已有background-color定义，先移除再追加（避免重复）
                    return '<p' + p1.replace(/(style\s*=\s*['"])([^'"]*)(['"])/i, '$1$2; background-color: transparent!important;$3') + '>'
                } else if (/style\s*=\s*['"]/.test(p1)) {
                    // 有style但无background-color，直接在末尾追加
                    return '<p' + p1.replace(/(style\s*=\s*['"][^'"]*)(['"])/, '$1; background-color: transparent!important;$2') + '>'
                } else {
                    // 无style属性，直接添加
                    return '<p' + p1 + ' style="background-color: transparent!important;">'
                }
            })
            .replace(/<span([^>]*)>/g, function (match, p1) {
                if (/style\s*=\s*['"][^'"]*background-color\s*:[^'"]*['"]/i.test(p1)) {
                    // 如果已有background-color定义，先移除再追加（避免重复）
                    return '<span' + p1.replace(/(style\s*=\s*['"])([^'"]*)(['"])/i, '$1$2; background-color: transparent!important;$3') + '>'
                } else if (/style\s*=\s*['"]/.test(p1)) {
                    // 有style但无background-color，直接在末尾追加
                    return '<span' + p1.replace(/(style\s*=\s*['"][^'"]*)(['"])/, '$1; background-color: transparent!important;$2') + '>'
                } else {
                    // 无style属性，直接添加
                    return '<span' + p1 + ' style="background-color: transparent!important;">'
                }
            })
            .replace(/<video/g, '<video style="width: 100%; max-width: 100%; height: auto;"')
            
    })
})
</script>

<style scoped lang="scss">
.classbox {
    .noticeone {
        width: 100%;
        padding: 30rpx;
        display: grid;
        border-bottom: 2rpx solid #f2f2f2;

        .txt {
            font-family: Source Han Sans;
            font-size: 24rpx;
            font-weight: normal;
            line-height: normal;
            letter-spacing: 2rpx;
            color: #ffffff;
        }

        .time {
            margin-top: 14rpx;
            margin-left: 10rpx;
            font-family: Source Han Sans;
            font-size: 24rpx;
            font-weight: normal;
            line-height: normal;
            letter-spacing: normal;
            color: #ffffff;
        }
    }
}
</style>
