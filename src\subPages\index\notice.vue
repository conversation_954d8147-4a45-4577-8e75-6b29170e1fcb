<template>
    <ex-header title="通知" background-color="#1a2b4a" text-color="#fff" mode="dark" />
    <view class="classbox">
        <view class="noticeone" v-for="(item, index) in noticeList" :key="index">
            <text class="txt">【通知】{{ item.title }}</text>
            <rich-text class="time" :nodes="item.content"></rich-text>
            <text class="time">发布时间：{{ item.create_time }}</text>
        </view>
    </view>
</template>

<script setup lang="ts">
import { articleDetail } from '@/api/index/index'

const noticeList = ref<any[]>([])

onLoad(async (e: any) => {
    await articleDetail(e.id).then((res: any) => {
        noticeList.value.push(res.data)
    })
    noticeList.value.forEach((ele) => {
        ele.content = ele.content.replace(/<img/gi, '<img style="max-width:100%!important"')
    })
})
</script>

<style scoped lang="scss">
.classbox {
    .noticeone {
        width: 100%;
        padding: 30rpx;
        display: grid;
        border-bottom: 2rpx solid #f2f2f2;

        .txt {
            font-family: Source Han Sans;
            font-size: 24rpx;
            font-weight: normal;
            line-height: normal;
            letter-spacing: 2rpx;
            color: #ffffff;
        }

        .time {
            margin-top: 14rpx;
            margin-left: 10rpx;
            font-family: Source Han Sans;
            font-size: 24rpx;
            font-weight: normal;
            line-height: normal;
            letter-spacing: normal;
            color: #ffffff;
        }
    }
}
</style>
