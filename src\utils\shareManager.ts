/**
 * 分享参数管理器
 * 解决 tabbar 页面只能使用 onShow 而无法使用 onLoad 获取分享参数的问题
 */

interface ShareParams {
    pid?: string
    [key: string]: any
}

class ShareManager {
    private static instance: ShareManager
    private shareParams: ShareParams = {}
    private isProcessed = false

    private constructor() {}

    static getInstance(): ShareManager {
        if (!ShareManager.instance) {
            ShareManager.instance = new ShareManager()
        }
        return ShareManager.instance
    }

    /**
     * 设置分享参数（在 App.vue 的 onLaunch 中调用）
     */
    setShareParams(params: ShareParams) {
        this.shareParams = { ...params }
        this.isProcessed = false
        console.log('ShareManager: 设置分享参数', params)
    }

    /**
     * 获取分享参数（在首页 onShow 中调用）
     */
    getShareParams(): ShareParams {
        return { ...this.shareParams }
    }

    /**
     * 检查是否有分享参数
     */
    hasShareParams(): boolean {
        return Object.keys(this.shareParams).length > 0
    }

    /**
     * 检查参数是否已被处理
     */
    isParamsProcessed(): boolean {
        return this.isProcessed
    }

    /**
     * 标记参数已处理
     */
    markAsProcessed() {
        this.isProcessed = true
        console.log('ShareManager: 参数已处理')
    }

    /**
     * 清除分享参数
     */
    clearShareParams() {
        this.shareParams = {}
        this.isProcessed = false
        console.log('ShareManager: 清除分享参数')
    }

    /**
     * 获取推荐人ID
     */
    getReferrerId(): string | undefined {
        return this.shareParams.pid
    }
}

export default ShareManager.getInstance()
