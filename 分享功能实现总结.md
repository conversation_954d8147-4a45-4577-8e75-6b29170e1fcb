# Uniapp 微信小程序全局分享功能实现总结

## 功能概述

已成功实现了 Uniapp 微信小程序的全局分享功能，支持分享给朋友和分享到朋友圈，并包含推荐人参数传递机制。

## 核心特性

### 1. 原生分享功能
- 使用微信小程序原生右上角分享功能
- 支持 `onShareAppMessage`（分享给朋友）
- 支持 `onShareTimeline`（分享到朋友圈）

### 2. 推荐人参数传递
- 分享时自动携带当前用户的 `member_id` 作为推荐人ID (`pid`)
- 被分享用户点击链接后，推荐人ID会被缓存到本地存储
- 登录时会将推荐人ID传递给后端API

### 3. 智能跳转策略
- **资讯详情页分享**：跳转到对应的资讯详情页面
- **其他页面分享**：统一跳转到首页，保证用户体验一致性

## 技术实现

### 1. 全局分享组合式函数 (`src/composables/useShare.ts`)

```typescript
// 核心接口定义
export interface UseShareOptions {
    title?: string                    // 分享标题
    imageUrl?: string                // 分享图片
    path?: string                    // 分享路径
    redirectToHome?: boolean         // 是否跳转首页
    extraQuery?: Record<string, string | number>  // 额外查询参数
    getTitleFn?: () => string        // 动态获取标题
    getImageUrlFn?: () => string     // 动态获取图片
    getExtraQueryFn?: () => Record<string, string | number>  // 动态获取参数
}

// 主要功能
- buildSharePath(): 构建分享路径，自动添加推荐人参数
- handleShareParams(): 处理分享参数，实现推荐人逻辑
- setupShareAppMessage(): 设置分享给朋友功能
- setupShareTimeline(): 设置分享到朋友圈功能
```

### 2. 参数流转机制

#### App.vue 参数捕获
```typescript
// 在 onLaunch 和 onShow 中捕获分享参数
if (options.query?.pid) {
    uni.setStorageSync('referrer_pid', options.query.pid)
    console.log('🔥 App - 保存推荐人ID到缓存:', options.query.pid)
}
```

#### 登录页面参数处理
```typescript
// 登录时将推荐人ID传递给后端
const referrerPid = uni.getStorageSync('referrer_pid')
if (referrerPid) {
    loginData.pid = referrerPid
    // 登录成功后清除缓存
    uni.removeStorageSync('referrer_pid')
}
```

### 3. 页面集成示例

#### 首页 (`src/pages/index/index.vue`)
```typescript
const { handleShareParams } = useShare({
    title: 'LNT(中国) - 专业培训平台，一起来学习吧！',
    imageUrl: '/static/logo.png',
    path: '/pages/index/index',
    redirectToHome: false  // 首页不需要跳转
})

onShow(async () => {
    await handleShareParams()  // 处理推荐人逻辑
    // 其他页面逻辑...
})
```

#### 资讯详情页 (`src/subPages/Information/details.vue`)
```typescript
const { handleShareParams } = useShare({
    title: '精彩资讯分享',
    imageUrl: '/static/logo.png',
    path: '/subPages/Information/details',
    redirectToHome: false,  // 资讯详情页不跳转到首页
    // 动态获取分享配置
    getTitleFn: () => details.value.title || '精彩资讯分享',
    getImageUrlFn: () => details.value.pic_path ? getImageUrl(details.value.pic_path) : getImageUrl('upload/file/logo.jpg'),
    getExtraQueryFn: () => ({ id: details.value.id })  // 动态传递资讯ID
})
```

#### 其他页面（活动、课程、我的）
```typescript
const { handleShareParams } = useShare({
    title: '页面特定标题',
    imageUrl: '/static/logo.png', 
    path: '/pages/xxx/index',
    redirectToHome: true  // 跳转到首页
})
```

## 已实现的页面

✅ **首页** (`src/pages/index/index.vue`)
- 完整的分享功能和推荐人逻辑

✅ **资讯详情页** (`src/subPages/Information/details.vue`)  
- 动态分享标题、图片和资讯ID参数
- 跳转到对应资讯详情页

✅ **活动页面** (`src/pages/activity/index.vue`)
- 分享后跳转到首页

✅ **课程页面** (`src/pages/course/index.vue`)
- 分享后跳转到首页

✅ **我的页面** (`src/pages/mine/index.vue`)
- 分享后跳转到首页

## 调试功能

所有分享相关操作都包含详细的控制台日志，使用 🔥 emoji 前缀便于识别：

```
🔥 App - 保存推荐人ID到缓存: 12345
🔥 首页 onShow - 开始处理分享参数
🔥 首页分享 - 当前用户ID: 12345
🔥 首页分享 - 分享路径: /pages/index/index?pid=12345
```

## 使用说明

1. **新页面添加分享功能**：
   - 导入 `useShare` 组合式函数
   - 配置分享选项
   - 在 `onShow` 中调用 `handleShareParams()`

2. **自定义分享内容**：
   - 使用 `getTitleFn`、`getImageUrlFn`、`getExtraQueryFn` 实现动态内容

3. **推荐人参数处理**：
   - 系统自动处理，无需额外配置
   - 登录API需要支持 `pid` 参数

## 技术优势

- **组合式函数设计**：代码复用性强，易于维护
- **TypeScript支持**：完整的类型定义，开发体验好
- **灵活配置**：支持静态和动态分享内容
- **统一的参数处理**：所有页面使用相同的推荐人逻辑
- **详细的调试日志**：便于问题排查和功能验证

## 注意事项

1. 确保微信小程序已配置分享域名
2. 分享图片路径需要是绝对路径或网络地址
3. 推荐人参数会在用户登录后自动清除
4. 所有分享链接都会经过参数处理，确保推荐关系正确建立
